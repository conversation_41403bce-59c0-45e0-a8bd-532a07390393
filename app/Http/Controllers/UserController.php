<?php

namespace App\Http\Controllers;

use App\Models\Account;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;

class UserController extends Controller
{
    public function getDataGameDecor(Request $request)
    {
        try {
	    $user = Auth::user();
            if (!$user) {
                return response()->json(['error' => 'Chua dang nhap'], 401);
            }

            $data = Account::query()->where('id', $user->id)->first();
            if (!$data) {
                return response()->json(['error' => 'User not found'], 404);
            }

            return response($data->data_game_decor, 200)
                ->header('Content-Type', 'application/json');
        } catch (\Exception $e) {
            return response()->json(['error' => 'Something went wrong. Please try again'], 500);
        }
    }

    public function updateDataGameDecor(Request $request)
    {   
        try {
            $data = Account::query()->where('id', $request->id)->first();
	    if (!$data) {
                return response()->json(['error' => 'User not found'], 404);
            }
	    $data->data_game_decor = $request;
   
            $data->save();
            return redirect()->back()->with('success', 'update user successfully.');

        } catch (Exception $e){
            return redirect()->back()->withInput()->with('error', 'Something went wrong. Please try again');
        }
    }
}
